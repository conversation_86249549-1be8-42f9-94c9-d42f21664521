#!/bin/bash

start=$SECONDS
skipFlag=false
buildStagingFlag=false
buildProdFlag=false
buildIntegrationFlag=false

# Check for '-j' argument
for arg in "$@"
do
    if [ "$arg" == "-j" ] ; then
        skipFlag=true
        break
    fi
    if [ "$arg" == "-b" ] ; then
        buildStagingFlag=true
        break
    fi
    if [ "$arg" == "-p" ] ; then
        buildProdFlag=true
        break
    fi
    if [ "$arg" == "-i" ] ; then
        buildIntegrationFlag=true
        break
    fi
done

echo "Cleaning and setting up Flutter workspace"
echo "--script does NOT upgrade major-versions"

rm -rf ./ios/Pods/
rm -f ./ios/Podfile.lock

flutter clean

rm -rf ~/Library/Developer/Xcode/DerivedData

./setup_all.sh

# Conditionally execute these commands
if [ "$skipFlag" = false ] ; then
    rm -rf ~/Library/Caches/CocoaPods/
    pod cache clean --all

    cd ios
    pod deintegrate
    rm Podfile.lock
    cd ..
    flutter pub cache repair
    flutter pub outdated
    flutter pub upgrade
    cd ios
    pod install
    cd ..
fi

if [ "$buildStagingFlag" = true ] ; then
  cd ios
#  bundle exec fastlane release branch:dev platform:ios flavor:staging pr:601
  cd ../android
  bundle exec fastlane release branch:dev platform:android flavor:staging pr:608
  cd ..
fi

if [ "$buildProdFlag" = true ] ; then
  cd ios
  bundle exec fastlane release branch:dev platform:ios flavor:prod pr:422
  cd ../android
  bundle exec fastlane release branch:dev platform:android flavor:prod pr:422
  cd ..
fi

if [ "$buildIntegrationFlag" = true ] ; then
  cd ios
  bundle exec fastlane release branch:dev platform:ios flavor:staging pr:435,436 appium:true
  cd ../android
  bundle exec fastlane release branch:dev platform:android flavor:staging pr:435,436 appium:true
  cd ..
fi

#flutter packages pub get
# flutter pub run intl_utils:generate
# flutter pub upgrade --major-versions

# cd ios && pod update && cd ..

# flutter pub run build_runner build --delete-conflicting-outputs
# echo "Building iOS project so have the needed files generated"
#flutter build ios

duration=$(( SECONDS - start ))
minutes=$((duration / 60))
seconds=$((duration % 60))
echo "Finished cleaning and setting up Flutter workspace. Took $minutes minutes and $seconds seconds."
